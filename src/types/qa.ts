export interface QAQuestion {
  id: string;
  question: string;
  answer: string;
  keywords: string[];
  priority: number;
}

export interface QACategory {
  id: string;
  name: string;
  icon: string;
  questions: QAQuestion[];
}

export interface QASettings {
  maxDisplayQuestions: number;
  defaultCategory: string;
  enableSearch: boolean;
  enableCategories: boolean;
}

export interface QAConfig {
  categories: QACategory[];
  settings: QASettings;
}

export interface QAContextType {
  config: QAConfig | null;
  loading: boolean;
  error: string | null;
  searchQuestions: (query: string) => QAQuestion[];
  getQuestionsByCategory: (categoryId: string) => QAQuestion[];
  getQuestionById: (questionId: string) => QAQuestion | null;
  getAnswerByQuestion: (question: string) => string | null;
}
