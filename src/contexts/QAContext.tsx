import React, { createContext, use<PERSON>ontext, ReactNode } from 'react';
import { QAConfig, QAQuestion, QAContextType } from '../types/qa';
import qaConfigData from '../data/qa-config.json';

// 类型断言，确保 JSON 数据符合 QAConfig 接口
const qaConfig = qaConfigData as QAConfig;

// 扩展 QAContextType 接口以包含额外的方法
export interface ExtendedQAContextType extends QAContextType {
  getHotQuestions: () => QAQuestion[];
}

// 创建 Context
const QAContext = createContext<ExtendedQAContextType | undefined>(undefined);

// QA Context Provider 组件
export const QAProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const config = qaConfig;
  const loading = false;
  const error = null;

  // 搜索问题的方法
  const searchQuestions = (query: string): QAQuestion[] => {
    if (!query.trim()) return [];
    
    const allQuestions = config.categories.flatMap(category => category.questions);
    const lowerQuery = query.toLowerCase();
    
    return allQuestions.filter(question => {
      // 检查问题标题
      if (question.question.toLowerCase().includes(lowerQuery)) {
        return true;
      }
      
      // 检查关键词
      if (question.keywords.some(keyword => 
        keyword.toLowerCase().includes(lowerQuery)
      )) {
        return true;
      }
      
      // 检查答案内容
      if (question.answer.toLowerCase().includes(lowerQuery)) {
        return true;
      }
      
      return false;
    });
  };

  // 根据分类获取问题
  const getQuestionsByCategory = (categoryId: string): QAQuestion[] => {
    const category = config.categories.find(cat => cat.id === categoryId);
    return category ? category.questions : [];
  };

  // 根据ID获取问题
  const getQuestionById = (questionId: string): QAQuestion | null => {
    const allQuestions = config.categories.flatMap(category => category.questions);
    return allQuestions.find(question => question.id === questionId) || null;
  };

  // 根据问题文本获取答案
  const getAnswerByQuestion = (questionText: string): string | null => {
    const allQuestions = config.categories.flatMap(category => category.questions);
    const foundQuestion = allQuestions.find(q => q.question === questionText);
    return foundQuestion ? foundQuestion.answer : null;
  };

  // 获取热门问题（按优先级排序）
  const getHotQuestions = (): QAQuestion[] => {
    const allQuestions = config.categories.flatMap(category => category.questions);
    return allQuestions
      .sort((a, b) => a.priority - b.priority)
      .slice(0, config.settings.maxDisplayQuestions);
  };

  const contextValue: ExtendedQAContextType = {
    config,
    loading,
    error,
    searchQuestions,
    getQuestionsByCategory,
    getQuestionById,
    getAnswerByQuestion,
    getHotQuestions,
  };

  return (
    <QAContext.Provider value={contextValue}>
      {children}
    </QAContext.Provider>
  );
};

// 自定义 hook 来使用 QA Context
export const useQAContext = (): ExtendedQAContextType => {
  const context = useContext(QAContext);
  if (context === undefined) {
    throw new Error('useQAContext must be used within a QAProvider');
  }
  return context;
};
