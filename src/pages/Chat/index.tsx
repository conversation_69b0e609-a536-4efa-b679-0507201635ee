import { CommentOutlined, SendOutlined, UserOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { Avatar, Button, Input, Space } from "antd";
import React, { useEffect, useRef, useState } from "react";
import avatar from "../../../public/avatar.svg";
import { useQAContext } from "../../contexts/QAContext";
import { QAQuestion } from "../../types/qa";
import c from "./index.module.less";

interface Message {
  id: string;
  content: string;
  sender: "user" | "assistant";
  timestamp: Date;
}

const Chat: React.FC = () => {
  // 使用 QA Context
  const qaData = useQAContext();

  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: "我是招聘智能小助手，请问有什么可以帮您？",
      sender: "assistant",
      timestamp: new Date("2024-01-01T09:44:00"),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<QAQuestion[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 获取热门问题
  const hotQuestions = qaData.getHotQuestions();

  // 处理输入变化，实时搜索
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputValue(value);

    if (value.trim()) {
      const results = qaData.searchQuestions(value);
      setSearchResults(results.slice(0, 5)); // 限制显示5个结果
      setShowSearchResults(results.length > 0);
    } else {
      setSearchResults([]);
      setShowSearchResults(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (content?: string) => {
    const messageContent = content || inputValue.trim();
    if (!messageContent || isLoading) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content: messageContent,
      sender: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setInputValue("");
    setIsLoading(true);

    // 使用智能匹配查找答案
    let answer = qaData.getAnswerByQuestion(messageContent);

    // 如果没有精确匹配，尝试搜索相关问题
    if (!answer) {
      const searchResults = qaData.searchQuestions(messageContent);
      if (searchResults.length > 0) {
        // 使用最相关的问题的答案
        answer = searchResults[0].answer;
      }
    }

    // 清除搜索结果
    setSearchResults([]);
    setShowSearchResults(false);

    setTimeout(() => {
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content:
          answer ||
          "我还没有学会这个问题,您可以点击我要留言】描述您的问题,我们将在3-5个工作日内为您解决或尝试询问我其他问题~",
        sender: "assistant",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  };

  return (
    <PageContainer>
      <div className={c.chatContainer}>
        <div className={c.chatWrapper}>
          {/* Messages Area */}
          <div className={c.messagesArea}>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`${c.messageItem} ${
                  message.sender === "user" ? c.userMessage : c.assistantMessage
                }`}
              >
                <div className={c.messageContent}>
                  <div className={c.messageAvatar}>
                    {message.sender === "assistant" ? (
                      <Avatar size={40} src={avatar}></Avatar>
                    ) : (
                      <Avatar size={32} icon={<UserOutlined />} />
                    )}
                  </div>
                  <div className={c.messageBubble}>
                    <div className={c.messageText}>{message.content}</div>
                    <div className={c.messageTime}>
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className={`${c.messageItem} ${c.assistantMessage}`}>
                <div className={c.messageContent}>
                  <div className={c.messageAvatar}>
                    <Avatar size={40} src={avatar}></Avatar>
                  </div>
                  <div className={c.messageBubble}>
                    <div className={c.messageText}>正在输入...</div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className={c.inputArea}>
            <Space.Compact style={{ width: "100%" }}>
              <Input.TextArea
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder="请输入..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                style={{
                  resize: "none",
                  borderRadius: "8px 0 0 8px",
                }}
                disabled={isLoading}
              />
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={() => handleSendMessage()}
                loading={isLoading}
                style={{
                  height: "auto",
                  borderRadius: "0 8px 8px 0",
                  minHeight: "32px",
                }}
                disabled={!inputValue.trim()}
              />
            </Space.Compact>
          </div>
        </div>
        <div className={c.chatOptions}>
          <div className={c.optionsHeader}>
            <span>热门回答</span>
          </div>
          <div className={c.options}>
            {hotQuestions.map((question) => (
              <div
                key={question.id}
                className={c.optionItem}
                onClick={() => handleSendMessage(question.question)}
              >
                <CommentOutlined /> {question.question}
              </div>
            ))}
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default Chat;
