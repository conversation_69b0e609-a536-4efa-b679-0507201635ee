import { CommentOutlined, SendOutlined, UserOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-components";
import { Avatar, Button, Input, Space } from "antd";
import React, { useEffect, useRef, useState } from "react";
import avatar from "../../../public/avatar.svg";
import qaConfig from "../../data/qa-config.json";
import c from "./index.module.less";

interface Message {
  id: string;
  content: string;
  sender: "user" | "assistant";
  timestamp: Date;
}

const Chat: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      content: "我是招聘智能小助手，请问有什么可以帮您？",
      sender: "assistant",
      timestamp: new Date("2024-01-01T09:44:00"),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 获取热门问题，按优先级排序并限制数量
  const getHotQuestions = () => {
    const allQuestions = qaConfig.categories.flatMap((category) =>
      category.questions.map((q) => ({
        ...q,
        categoryName: category.name,
        categoryId: category.id,
      }))
    );

    return allQuestions
      .sort((a, b) => a.priority - b.priority)
      .slice(0, qaConfig.settings.maxDisplayQuestions);
  };

  const hotQuestions = getHotQuestions();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async (content?: string) => {
    const messageContent = content || inputValue.trim();
    if (!messageContent || isLoading) return;

    const newMessage: Message = {
      id: Date.now().toString(),
      content: messageContent,
      sender: "user",
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, newMessage]);
    setInputValue("");
    setIsLoading(true);

    // 查找预设答案
    const foundQuestion = qaConfig.categories
      .flatMap((category) => category.questions)
      .find((q) => q.question === messageContent);

    setTimeout(() => {
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content:
          foundQuestion?.answer ||
          "我还没有学会这个问题,您可以点击我要留言】描述您的问题,我们将在3-5个工作日内为您解决或尝试询问我其他问题~",
        sender: "assistant",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, assistantMessage]);
      setIsLoading(false);
    }, 1000);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  };

  return (
    <PageContainer>
      <div className={c.chatContainer}>
        <div className={c.chatWrapper}>
          {/* Messages Area */}
          <div className={c.messagesArea}>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`${c.messageItem} ${
                  message.sender === "user" ? c.userMessage : c.assistantMessage
                }`}
              >
                <div className={c.messageContent}>
                  <div className={c.messageAvatar}>
                    {message.sender === "assistant" ? (
                      <Avatar size={40} src={avatar}></Avatar>
                    ) : (
                      <Avatar size={32} icon={<UserOutlined />} />
                    )}
                  </div>
                  <div className={c.messageBubble}>
                    <div className={c.messageText}>{message.content}</div>
                    <div className={c.messageTime}>
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className={`${c.messageItem} ${c.assistantMessage}`}>
                <div className={c.messageContent}>
                  <div className={c.messageAvatar}>
                    <Avatar size={40} src={avatar}></Avatar>
                  </div>
                  <div className={c.messageBubble}>
                    <div className={c.messageText}>正在输入...</div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input Area */}
          <div className={c.inputArea}>
            <Space.Compact style={{ width: "100%" }}>
              <Input.TextArea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="请输入..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                style={{
                  resize: "none",
                  borderRadius: "8px 0 0 8px",
                }}
                disabled={isLoading}
              />
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={() => handleSendMessage()}
                loading={isLoading}
                style={{
                  height: "auto",
                  borderRadius: "0 8px 8px 0",
                  minHeight: "32px",
                }}
                disabled={!inputValue.trim()}
              />
            </Space.Compact>
          </div>
        </div>
        <div className={c.chatOptions}>
          <div className={c.optionsHeader}>
            <span>热门回答</span>
          </div>
          <div className={c.options}>
            {hotQuestions.map((question) => (
              <div
                key={question.id}
                className={c.optionItem}
                onClick={() => handleSendMessage(question.question)}
              >
                <CommentOutlined /> {question.question}
              </div>
            ))}
          </div>
        </div>
      </div>
    </PageContainer>
  );
};

export default Chat;
