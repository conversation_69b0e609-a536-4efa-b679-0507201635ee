import type { Settings as LayoutSettings } from "@ant-design/pro-components";
import "@ant-design/v5-patch-for-react-19";
import type { RequestConfig, RunTimeLayoutConfig } from "@umijs/max";
import defaultSettings from "../config/defaultSettings";
import { QAProvider } from "./contexts/QAContext";
import { errorConfig } from "./requestErrorConfig";

const isDev = process.env.NODE_ENV === "development";
const isDevOrTest = isDev || process.env.CI;

/**
 * @see https://umijs.org/docs/api/runtime-config#getinitialstate
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
}> {
  return {
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({
  initialState,
  setInitialState,
}) => {
  console.log(initialState?.settings);
  return {
    actionsRender: () => [],
    // footerRender: () => <Footer />,
    // 隐藏菜单栏
    menuRender: false,
    // 隐藏菜单头部
    // ... existing code ...
    menuHeaderRender: (logo, title, props) => {
      const isMobile = props?.collapsed;
      return (
        <div style={{ display: "flex", alignItems: "center" }}>
          {logo}
          {!isMobile && title}
        </div>
      );
    },
    // ... existing code ...
    bgLayoutImgList: [
      {
        src: "https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr",
        left: 85,
        bottom: 100,
        height: "303px",
      },
      {
        src: "https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr",
        bottom: -68,
        right: -45,
        height: "303px",
      },
      {
        src: "https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr",
        bottom: 0,
        left: 0,
        width: "331px",
      },
    ],
    // links: isDevOrTest
    //   ? [
    //       <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
    //         <LinkOutlined />
    //         <span>OpenAPI 文档</span>
    //       </Link>,
    //     ]
    //   : [],
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;

      // 容器样式 - 使用 flex 布局后简化样式
      const containerStyle: React.CSSProperties = {
        width: "100%",
        minHeight: "100%",
        backgroundColor: "white",
        borderRadius: "16px",
        boxShadow: "0 8px 32px rgba(0, 0, 0, 0.12)",
        overflow: "hidden",
        position: "relative",
        transition: "all 0.3s ease",
        boxSizing: "border-box",
      };

      // CSS variables for responsive design - 使用 flex 布局解决滚动条间距问题
      const cssVariables = `
        body {
          display: flex;
          justify-content: center;
          align-items: flex-start;
          min-height: 100vh;
        //   padding: 12.5vh 0;
          box-sizing: border-box;
        }

        #root {
          width: 50vw;
          max-width: 1400px;
          min-height: 75vh;
        }

        @media (max-width: 1200px) {
          // body {
          //   padding: 10vh 0;
          // }
          #root {
            width: 70vw;
          }
        }

        @media (max-width: 768px) {
          // body {
          //   padding: 5vh 0;
          // }
          #root {
            width: 90vw;
            min-height: 90vh;
          }
        }

        @media (max-width: 480px) {
          // body {
          //   padding: 2vh 0;
          // }
          #root {
            width: 100vw;
            min-height: 100vh;
          }
        }
      `;

      return (
        <QAProvider>
          <style dangerouslySetInnerHTML={{ __html: cssVariables }} />
          <div style={containerStyle}>{children}</div>
        </QAProvider>
      );
    },
    ...initialState?.settings,
    logo: () => <img src="/logo.svg" alt="logo" style={{ height: "32px" }} />,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request: RequestConfig = {
  baseURL: isDev ? "" : "https://proapi.azurewebsites.net",
  ...errorConfig,
};
